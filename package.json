{"name": "pukpara-frontend", "private": true, "sideEffects": false, "type": "module", "scripts": {"dev": "vite dev", "build": "vite build && tsc --noEmit", "start": "node .output/server/index.mjs", "deploy": "npm run build && wrangler deploy", "cf-typegen": "wrangler types --env-interface Env", "lint": "biome lint .", "lint:fix": "biome lint --write .", "format": "biome format .", "format:fix": "biome format --write .", "check": "biome check .", "check:fix": "biome check --write ."}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@tailwindcss/postcss": "^4.1.11", "@tanstack/react-router": "^1.127.0", "@tanstack/react-router-devtools": "^1.127.0", "@tanstack/react-start": "^1.127.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@biomejs/biome": "^2.1.1", "@types/node": "^22.16.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "postcss": "^8.5.1", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4", "wrangler": "^4.24.3"}}