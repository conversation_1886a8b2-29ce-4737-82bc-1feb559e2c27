{"name": "pukpara-frontend", "private": true, "sideEffects": false, "type": "module", "scripts": {"dev": "vite dev", "build": "vite build && tsc --noEmit", "start": "node .output/server/index.mjs", "deploy": "npm run build && wrangler deploy", "cf-typegen": "wrangler types --env-interface Env"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@tailwindcss/postcss": "^4.1.11", "@tanstack/react-router": "^1.127.0", "@tanstack/react-router-devtools": "^1.127.0", "@tanstack/react-start": "^1.127.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^22.5.4", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.6.0", "postcss": "^8.5.1", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "typescript": "^5.7.2", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4", "wrangler": "^4.24.3"}}