{"$schema": "https://biomejs.dev/schemas/2.1.1/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "includes": ["src/**/*", "*.ts", "*.tsx", "*.js", "*.jsx", "*.json"]}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 80, "attributePosition": "auto", "bracketSpacing": true}, "linter": {"enabled": true, "rules": {"recommended": true, "a11y": {"recommended": true}, "complexity": {"recommended": true, "noExtraBooleanCast": "error", "noUselessCatch": "error", "noUselessTypeConstraint": "error"}, "correctness": {"recommended": true, "noUnusedVariables": "error", "useExhaustiveDependencies": "warn"}, "performance": {"recommended": true}, "security": {"recommended": true}, "style": {"recommended": true, "noNegationElse": "off", "useImportType": "error", "useConst": "error", "useTemplate": "error"}, "suspicious": {"recommended": true, "noExplicitAny": "warn", "noArrayIndexKey": "warn"}}}, "javascript": {"formatter": {"jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "es5", "semicolons": "asNeeded", "arrowParentheses": "always", "bracketSameLine": false, "quoteStyle": "double", "attributePosition": "auto"}}, "json": {"formatter": {"enabled": true}}, "css": {"formatter": {"enabled": true}}, "overrides": [{"includes": ["src/routeTree.gen.ts"], "linter": {"enabled": false}, "formatter": {"enabled": false}}]}